# Keep existing schemas unchanged
# assets_schema = {
#   "current_assets": {
#     "asset_type": [
#       {"name": "cash_and_cash_equivalents", "value": ""},
#       {"name": "inventory", "value": ""},
#       {"name": "other_assets", "value": ""},
#       {"name": "trade_receivable", "value": ""}
#     ]
#   },
#   "date": "",
#   "other_non_current_assets": {
#     "asset_type": [
#       {"name": "capital_work_in_progress", "value": ""},
#       {"name": "intangible_assets", "value": ""},
#       {"name": "financial_assets", "value": ""},
#       {"name": "other_non_current_assets", "value": ""},
#       {"name": "income_tax_assets_net", "value": ""}
#     ]
#   },
#   "property_plant_equipment": {
#     "asset_type": [
#       {"name": "property_plant_equipment", "value": ""}
#     ]
#   }
# }

# # 🔧 UPDATED: Assets prompt with year filtering
# def create_assets_prompt(target_year: int):
#     return f"""
# Please extract asset-related data for the company using the schema provided below.

# **CRITICAL YEAR INSTRUCTION**:
# - Extract data ONLY for the year {target_year}
# - If multiple years are present, extract ONLY {target_year} data
# - Ignore all other years' data completely
# - The date field should reflect {target_year} financial year end

# **Important Instructions**:
# 1. Extract only the **company-specific data**. Ignore group or consolidated values.
# 2. If both company and group data are available, use **only the company** data.
# 3. If asset values are given in **millions or billions**, convert them to **whole numbers** (e.g., 35 million = 35000000).
# 4. Output must strictly follow the **JSON structure provided below**, keeping all field names and hierarchy intact.
# 5. **YEAR FILTER: Only extract data for {target_year}. Ignore all other years.**

# Here is the schema you must follow:
# {assets_schema}
# """

# cashflows_schema = {
#   "cashflows": [
#     {
#       "capex": "",
#       "cash_from_operations": "",
#       "free_cash_flow": "",
#       "year": ""
#     }
#   ]
# }

# # 🔧 UPDATED: Cashflows prompt with year filtering
# def create_cashflows_prompt(target_year: int):
#     return f"""
# Please extract capital expenditure and related data for the **company** based on the schema provided below.

# **CRITICAL YEAR INSTRUCTION**:
# - Extract data ONLY for the year {target_year}
# - Return ONLY ONE record for year {target_year}
# - If multiple years are shown, extract ONLY {target_year} data
# - Ignore all other years completely

# **Important Instructions**:
# 1. Extract only the **company-specific data**. Ignore group or consolidated data.
# 2. If both company and group data are available, use **only the company** data.
# 3. If any of the values are presented in **millions or billions**, convert them into **whole numbers**.  
#    (e.g., 23 million = 23000000, 15 billion = 15000000000)
# 4. **SINGLE YEAR ONLY**: Return only one record for {target_year}. Do not extract multiple years.
# 5. The output must strictly follow the **JSON structure** defined below, keeping all field names exactly the same.
# 6. Set the year field to: {target_year}

# Here is the schema you must follow:
# {cashflows_schema}
# """

# schema_revenue_expenses = {
#   "revenue_expenses": [
#     {
#       "expenses": {
#         "corporate_tax": "",
#         "depreciation": "",
#         "exceptions_before_tax": "",
#         "fuel_expenses": "",
#         "interest": "",
#         "o_m_expenses": ""
#       },
#       "revenue": {
#         "other_income": "",
#         "revenue_operations": ""
#       },
#       "year": ""
#     }
#   ]
# }

# # 🔧 UPDATED: Revenue expenses prompt with year filtering
# def create_revenue_expenses_prompt(target_year: int):
#     return f"""
# Please extract revenue and expense details for the **company** using the schema provided below.

# **CRITICAL YEAR INSTRUCTION**:
# - Extract data ONLY for the year {target_year}
# - Return ONLY ONE record for year {target_year}
# - If multiple years are available, extract ONLY {target_year} data
# - Ignore all other years completely

# **Important Instructions**:
# 1. Extract only the **company-specific data**. Ignore any group or consolidated values.
# 2. If both company and group data are available, use **only the company** data.
# 3. If values are mentioned in **millions or billions**, convert them into **whole numbers**.  
#    (e.g., 42 million = 42000000, 11 billion = 11000000000)
# 4. **SINGLE YEAR ONLY**: Return only one record for {target_year}. Do not extract multiple years.
# 5. The output must strictly follow the **JSON structure** provided below, maintaining all field names and hierarchy exactly.
# 6. Set the year field to: {target_year}

# Here is the schema you must follow:
# {schema_revenue_expenses}
# """

# credit_rating_schema = {
#   "credit_rating": [
#     {
#       "agency": "",
#       "name": "",
#       "yearwise_rating": [
#         {"rating": "", "rating_trunc": "", "year": ""},
#       ]
#     }
#   ],
#   "credit_rating_note": "",
#   "currency": ""
# }

# # 🔧 UPDATED: Credit rating prompt with year filtering
# def create_credit_rating_prompt(target_year: int):
#     return f"""
# Your task is to extract the **credit rating** for the year {target_year} only.

# **CRITICAL YEAR INSTRUCTION**:
# - Extract credit rating data ONLY for the year {target_year}
# - If multiple years are shown, focus ONLY on {target_year}
# - For yearwise_rating array, include ONLY {target_year} data

# **Instructions**:
# 1. Use the instrument with the **highest rated amount** or **most significant exposure** per agency when multiple instruments are listed.
# 2. For the `credit_rating_note`, extract the instrument type on which the rating is based.  
#    For example, if ratings are based on a **Long Term Loan Facility**, then the note should be:  
#    `"Credit ratings displayed below are for Long Term Loan Facility."`
# 3. Extract the **currency** mentioned in the credit rating section (e.g., INR, USD, etc.).
# 4. **YEAR FILTER**: In yearwise_rating, include only records for {target_year}.
# 5. You should not miss any fields if the data is not available means put an empty string for that field.
# 6. The output must follow the **JSON structure** exactly as defined in the schema below.

# Here is the schema you must follow:
# {credit_rating_schema}
# """

# # 🔧 DYNAMIC PROMPT CREATION FUNCTIONS
# def get_dynamic_prompts(target_year: int):
#     """Get prompts configured for specific target year"""
#     return {
#         "assets_prompt": create_assets_prompt(target_year),
#         "cashflows_prompt": create_cashflows_prompt(target_year),
#         "revenue_expenses_prompt": create_revenue_expenses_prompt(target_year),
#         "credit_rating_prompt": create_credit_rating_prompt(target_year)
#     }

# Legacy support - keep original prompts with default behavior
# assets_prompt = create_assets_prompt(2024)
# cashflows_prompt = create_cashflows_prompt(2024)
# revenue_expenses_prompt = create_revenue_expenses_prompt(2024)
# credit_rating_prompt = create_credit_rating_prompt(2024)

assets_schema = {
  "current_assets": {
    "asset_type": [
      {"name": "cash_and_cash_equivalents", "value": ""},
      {"name": "inventory", "value": ""},
      {"name": "other_assets", "value": ""},
      {"name": "trade_receivable", "value": ""}
    ]
  },
  "date": "",
  "other_non_current_assets": {
    "asset_type": [
      {"name": "capital_work_in_progress", "value": ""},
      {"name": "intangible_assets", "value": ""},
      {"name": "financial_assets", "value": ""},
      {"name": "other_non_current_assets", "value": ""},
      {"name": "income_tax_assets_net", "value": ""}
    ]
  },
  "property_plant_equipment": {
    "asset_type": [
      {"name": "property_plant_equipment", "value": ""}
    ]
  }
}

assets_prompt = f"""
Please extract asset-related data for the company using the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore group or consolidated values.
2. If both company and group data are available, use **only the company** data.
3. If any of the values are presented in **thousands, lakhs, crores, millions, billions, or trillions**, convert them to **whole numbers** using the appropriate multiplier. Refer to the column heading or section title in the image to determine the unit. Use the following conversions:

        - thousand = 1,000  
        - lakh = 100,000  
        - crore = 10,000,000  
        - million = 1,000,000  
        - billion = 1,000,000,000  
        - trillion = 1,000,000,000,000  

        For example:
        - 23 million → 23000000  
        - 15 billion → 15000000000  
        - 25 crore → *********
        - 4.5L → 450000  
        - 1.2B → 1200000000

      Convert all such values into plain whole numbers with no suffixes.4. If the data for a specific year has been restated or revised, that year's data should be excluded from extraction.
5. Output must strictly follow the **JSON structure provided below**, keeping all field names and hierarchy intact.
6. Should not omit any keys—if data is unavailable, use an empty string ("") or null as appropriate, and do not infer or assume values.Please return the empty schema as it is provided in the prompt.if the data is not available(Please do not forget this point).
7. All "value" fields must be returned as **numbers**, not strings. if a value is unavailable, use null — do not use empty strings or placeholder text.
8. In the date field, return only in this format DD/MM/YYYY (eg: 31/12/2024 for the 31st of December 2024).
Here is the schema you must follow:
{assets_schema}
"""

cashflows_schema = {
  "cashflows": [
    {
      "capex": "",
      "cash_from_operations": "",
      "free_cash_flow": "",
      "year": ""
    }
  ]
}

cashflows_prompt = f"""
Please extract capital expenditure and related data for the **company** based on the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore group or consolidated data.
2. If both company and group data are available, use **only the company** data.
3. If any of the values are presented in **thousands, lakhs, crores, millions, billions, or trillions**, convert them to **whole numbers** using the appropriate multiplier. Refer to the column heading or section title in the image to determine the unit. Use the following conversions:

        - thousand = 1,000  
        - lakh = 100,000  
        - crore = 10,000,000  
        - million = 1,000,000  
        - billion = 1,000,000,000  
        - trillion = 1,000,000,000,000  

        For example:
        - 23 million → 23000000  
        - 15 billion → 15000000000  
        - 25 crore → *********
        - 4.5L → 450000  
        - 1.2B → 1200000000

        Convert all such values into plain whole numbers with no suffixes.

4. If data for two different years is provided, extract and return both years' data, formatted according to the specified schema for each year.
   However, if the data for a particular year is clearly marked as restated or revised, that year's data should be excluded from extraction.
   If the data is direct (i.e., not restated or revised), it can be extracted.
5. Please return the **JSON structure** provided below as it is provided, keeping all field names and hierarchy intact.if the data is not available(Please do not forget this point).
6. Return only the year value from the date — ignore the month and day.
7. All "value" fields must be returned as **numbers**, not strings. f a value is unavailable, use null — do not use empty strings or placeholder text.
8. you should not put the - (negative sighn) in the value field.

Here is the schema you must follow:
{cashflows_schema}
"""
schema_revenue_expenses = {
  "revenue_expenses": [
    {
      "expenses": {
        "corporate_tax": "",
        "depreciation": "",
        "exceptions_before_tax": "",
        "fuel_expenses": "",
        "interest": "",
        "o_m_expenses": ""
      },
      "revenue": {
        "other_income": "",
        "revenue_operations": ""
      },
      "year": ""
    }
  ]
}

revenue_expenses_prompt = f"""
Please extract revenue and expense details for the **company** using the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore any group or consolidated values.
2. If both company and group data are available, use **only the company** data.
3. If any of the values are presented in **thousands, lakhs, crores, millions, billions, or trillions**, convert them to **whole numbers** using the appropriate multiplier. Refer to the column heading or section title in the image to determine the unit. Use the following conversions:

        - thousand = 1,000  
        - lakh = 100,000  
        - crore = 10,000,000  
        - million = 1,000,000  
        - billion = 1,000,000,000  
        - trillion = 1,000,000,000,000  

        For example:
        - 23 million → 23000000  
        - 15 billion → 15000000000  
        - 25 crore → *********
        - 4.5L → 450000  
        - 1.2B → 1200000000

      Convert all such values into plain whole numbers with no suffixes.
4. If data for two different years is provided, extract and return both years' data, formatted according to the specified schema for each year.
   However, if the data for a particular year is clearly marked as restated or revised, that year's data should be excluded from extraction.
   If the data is direct (i.e., not restated or revised), it can be extracted.
   6. Return only the year value from the date — ignore the month and day.
5. Please return the **JSON structure** provided below as it is provided, keeping all field names and hierarchy intact.if the data is not available(Please do not forget this point).
6.Return only the year value from the date — ignore the month and day
7.All "value" fields must be returned as **numbers**, not strings. f a value is unavailable, use null — do not use empty strings or placeholder text.
8. Do not put the - (negative sighn) in the value field.

Here is the schema you must follow:
{schema_revenue_expenses}
"""

credit_rating_schema = {
  "credit_rating": [
    {
      "agency": "",
      "name": "",
      "yearwise_rating": [
        {"rating": "", "rating_trunc": "", "year": ""},
      ]
    }
  ],
  "credit_rating_note": "",
  "currency": ""
}

credit_rating_prompt = f"""
Your task is to extract the **year-wise credit rating** for each credit rating agency based on the column titled **"Original Credit Rating / Outlook"**.

**Instructions**:
1. Use the instrument with the **highest rated amount** or **most significant exposure** per agency when multiple instruments are listed.
2. For the `credit_rating_note`, extract the instrument type on which the rating is based.  
   For example, if ratings are based on a **Long Term Loan Facility**, then the note should be:  
   `"Credit ratings displayed below are for Long Term Loan Facility."`
3. Extract the **currency** mentioned in the credit rating section (e.g., INR, USD, etc.).
4. Should not omit any keys—if data is unavailable, use an empty string ("") or null as appropriate, and do not infer or assume values.please return the empty schema as it is provided in the prompt if the data is not available.
5. Return only the year value from the date — ignore the month and day
6.Extract year-wise credit ratings exactly as per the schema; if any field is missing or unavailable, set its value to an empty string or null, do not guess, assume, You should not use placeholder values like "Agency Name" or "AAA", and return only the empty string if the data is not available.

Here is the schema you must follow:
{credit_rating_schema}
"""