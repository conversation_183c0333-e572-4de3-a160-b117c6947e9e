from fastapi import Fast<PERSON><PERSON>, Form, BackgroundTasks, HTTPException
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Optional
from pathlib import Path
from datetime import datetime, timezone
import uuid
import os
import asyncio
import logging
import traceback
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from process_output_file import delete_folders_in_current_directory

# Pipeline imports
from multiyear_complete_integration_testing import CompleteIntegratedPipeline

# ========================
# Logging Configuration
# ========================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("financial_pipeline_clem_api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ========================
# App Initialization
# ========================
app = FastAPI(title="CLEM Transition Financial API", version="1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

job_tracker: Dict[str, Dict] = {}
s3_client = None

# ========================
# Utility Functions
# ========================
def get_current_utc_timestamp() -> str:
    return datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")

def generate_job_id() -> str:
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"job_{timestamp}_{unique_id}"

def validate_plant_name(plant_name: str) -> str:
    if not plant_name or len(plant_name.strip()) < 2:
        raise ValueError("Plant name must be at least 2 characters")
    return plant_name.strip().replace('/', '_').replace('\\', '_')

async def upload_to_s3(file_path: str, s3_key: str, bucket_name: str = None) -> str:
    if not bucket_name:
        bucket_name = os.getenv("S3_BUCKET_NAME", "aws-demo-22")
    try:
        s3_client.upload_file(file_path, bucket_name, s3_key)
        s3_url = f"s3://{bucket_name}/{s3_key}"
        logger.info(f"✅ Uploaded to S3: {s3_url}")
        return s3_url
    except ClientError as e:
        logger.error(f"❌ S3 upload failed: {e}")
        raise

async def upload_files_to_s3(plant_name: str, country_name: str, entity_id: str) -> Dict[str, str]:
    bucket_name = os.getenv("S3_BUCKET_NAME", "aws-demo-22")
    safe_plant_name = plant_name.replace(" ", "_").replace("/", "_")
    s3_prefix = f"{country_name}/{entity_id}"
    results = {}

    # Smart directory detection for final_output
    final_output_base = Path("final_output")
    final_output_dir = None

    logger.info(f"🔍 Looking for final_output directory for plant: '{plant_name}'")

    # Try multiple directory patterns
    possible_dirs = [
        final_output_base / safe_plant_name,
        final_output_base / plant_name.replace(" ", "_"),
        final_output_base / plant_name.upper().replace(" ", "_"),
        final_output_base / plant_name.lower().replace(" ", "_"),
    ]

    # Also check if there's any directory in final_output
    if final_output_base.exists():
        for subdir in final_output_base.iterdir():
            if subdir.is_dir():
                possible_dirs.append(subdir)

    # Find the first existing directory
    for dir_path in possible_dirs:
        if dir_path.exists() and dir_path.is_dir():
            final_output_dir = dir_path
            logger.info(f"✅ Found final_output directory: {final_output_dir}")
            break

    if not final_output_dir:
        # Fallback to output_part2 directory
        logger.warning("⚠️ final_output directory not found, checking output_part2...")
        output_part2_base = Path("output_part2")
        if output_part2_base.exists():
            for subdir in output_part2_base.iterdir():
                if subdir.is_dir() and safe_plant_name.lower() in subdir.name.lower():
                    return await upload_from_output_part2(subdir, s3_prefix, safe_plant_name, bucket_name)

        raise FileNotFoundError(f"No output directory found for {safe_plant_name}")

    # Upload files from final_output directory
    for suffix, key in [("financial_details.json", "primary_url"),
                        ("assumptions.json", "assumptions_url"),
                        ("static_data.json", "static_url")]:
        for file in final_output_dir.glob(f"*{suffix}"):
            s3_key = f"{s3_prefix}/{safe_plant_name}_{suffix}"
            results[key] = await upload_to_s3(str(file), s3_key, bucket_name)
            logger.info(f"✅ Uploaded {suffix}: {file}")
            break

    return results

async def upload_from_output_part2(output_dir: Path, s3_prefix: str,
                                  safe_plant_name: str, bucket_name: str) -> Dict[str, str]:
    """Upload from output_part2 folder (fallback method)"""
    results = {}

    logger.info(f"📁 Uploading from output_part2 directory: {output_dir}")

    # Look for financial JSON files
    financial_files = list(output_dir.glob("*_financial.json"))
    if financial_files:
        financial_file = str(financial_files[0])
        primary_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_details.json"
        results["primary_url"] = await upload_to_s3(financial_file, primary_s3_key, bucket_name)
        logger.info(f"✅ Uploaded financial (fallback): {financial_file}")

    # Look for assumptions files
    assumptions_files = list(output_dir.glob("*_financial_assumptions.json"))
    if assumptions_files:
        assumptions_file = str(assumptions_files[0])
        assumptions_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_assumptions.json"
        results["assumptions_url"] = await upload_to_s3(assumptions_file, assumptions_s3_key, bucket_name)
        logger.info(f"✅ Uploaded assumptions (fallback): {assumptions_file}")

    # Create placeholder static data if needed
    if "static_url" not in results:
        static_s3_key = f"{s3_prefix}/static_data.json"
        placeholder_data = {"message": "Static data not available", "source": "fallback"}
        # Create a temporary file for the placeholder
        import tempfile
        import json
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
            json.dump(placeholder_data, tmp_file)
            tmp_file_path = tmp_file.name

        results["static_url"] = await upload_to_s3(tmp_file_path, static_s3_key, bucket_name)

        # Clean up temporary file
        import os
        os.unlink(tmp_file_path)

    return results

async def process_clem_pipeline(job_id: str, plant_name: str, country_name: str, entity_id: str):
    try:
        update_job_status(job_id, "processing", 10, "Initializing pipeline...")
        pipeline = CompleteIntegratedPipeline()

        download_dir = Path("downloads")
        pdf_files = list(download_dir.glob("*.pdf"))
        if not pdf_files:
            raise FileNotFoundError("No PDF files found in downloads/")

        local_pdfs = [str(p) for p in pdf_files]

        financial_path, assumptions_path, results = await pipeline.process_complete_pipeline(
            plant_name=plant_name,
            entity_id=entity_id,
            local_pdfs=local_pdfs
        )

        final_output_dir = Path("final_output") / plant_name.replace(" ", "_")
        start_time = datetime.now()
        timeout = 300

        while not final_output_dir.exists() and (datetime.now() - start_time).total_seconds() < timeout:
            logger.info(f"⏳ Waiting for final_output: {final_output_dir}")
            await asyncio.sleep(10)

        s3_results = await upload_files_to_s3(plant_name, country_name, entity_id)

        successful_years = results.get("merged_results", {}).get("successful_years", [])
        total_years = len(results.get("year_results", []))

        final_results = {
            "primary_output": {"url": s3_results.get("primary_url", "")},
            "assumptions_output": {"url": s3_results.get("assumptions_url", "")},
            "static_data": {"url": s3_results.get("static_url", "")},
            "processed_years": successful_years,
            "success_rate": f"{len(successful_years)}/{total_years} years",
            "processing_summary": {
                "pipeline_mode": "clem_manual",
                "processing_time": get_current_utc_timestamp()
            }
        }

        update_job_status(job_id, "completed", 100, "Processing completed", results=final_results)
        logger.info(f"✅ Job {job_id} completed successfully")
        delete_folders_in_current_directory()

    except Exception as e:
        logger.error(traceback.format_exc())
        update_job_status(job_id, "failed", 0, "Processing failed", error=str(e))


# ========================
# Job Status Tracker
# ========================
def update_job_status(job_id: str, status: str, progress: int, current_step: str, results: Dict = None, error: str = None):
    if job_id in job_tracker:
        job_tracker[job_id].update({
            "status": status,
            "progress": progress,
            "current_step": current_step,
            "last_updated": get_current_utc_timestamp()
        })
        if results:
            job_tracker[job_id]["results"] = results
        if error:
            job_tracker[job_id]["error"] = error


@app.post("/api/process/clem_trans_financial")
async def process_clem_trans_financial(
    background_tasks: BackgroundTasks,
    plant_name: str = Form(...),
    country_name: str = Form(...),
    entity_id: str = Form(...)
):
    try:
        plant_name = validate_plant_name(plant_name)
        job_id = generate_job_id()

        job_tracker[job_id] = {
            "status": "processing",
            "progress": 0,
            "current_step": "Starting CLEM processing...",
            "timestamp": get_current_utc_timestamp(),
            "last_updated": get_current_utc_timestamp(),
            "user": "CLEM Runner",
            "country_name": country_name,
            "entity_id": entity_id,
            "plant_name": plant_name,
            "mode": "clem_manual",
            "results": None,
            "error": None
        }

        background_tasks.add_task(process_clem_pipeline, job_id, plant_name, country_name, entity_id)

        return JSONResponse({
            "job_id": job_id,
            "status": "processing",
            "timestamp": get_current_utc_timestamp(),
            "message": f"Started processing for {plant_name} using CLEM TRANS pipeline",
            "mode": "clem_manual",
            "source": "downloads folder"
        })

    except Exception as e:
        logger.error(f"❌ Error starting CLEM job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start processing: {str(e)}")

@app.get("/api/jobs/{job_id}")
async def get_job_status(job_id: str):
    if job_id not in job_tracker:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")
    return job_tracker[job_id]

@app.on_event("startup")
async def startup_event():
    global s3_client
    logger.info("🚀 Starting CLEM TRANS Financial API")
    required_vars = ["AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"]
    for var in required_vars:
        if not os.getenv(var):
            raise RuntimeError(f"Missing required environment variable: {var}")
    s3_client = boto3.client(
        's3',
        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
        region_name=os.getenv('AWS_REGION', 'us-east-1')
    )
    try:
        s3_client.head_bucket(Bucket=os.getenv("S3_BUCKET_NAME", "aws-demo-22"))
        logger.info("✅ S3 connection verified")
    except Exception as e:
        logger.warning(f"⚠️ S3 check failed: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("clem_trans_financial_api:app", host="0.0.0.0", port=8000, reload=True)